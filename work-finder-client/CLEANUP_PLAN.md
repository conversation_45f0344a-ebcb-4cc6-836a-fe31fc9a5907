# Kế hoạch dọn dẹp và cải tiến cấu trúc

## Phase 1: Chuẩn bị môi trường mới

1. ✅ Tạo structure mới (features/, atomic design)
2. ✅ Setup build tools (prettier, husky, jest)
3. ✅ <PERSON><PERSON><PERSON> hình next.config.ts, tailwind.config.ts, eslint

## Phase 2: Tạo structure mới

1. <PERSON><PERSON><PERSON> thư mục atomic design
2. Tạo thư mục features
3. Tạo type hierarchy

## Phase 3: Di chuyển components

1. Di chuyển UI components -> atoms
2. <PERSON> chuyển shared components -> molecules
3. Di chuyển sections -> organisms
4. Di chuyển layout -> templates

## Phase 4: <PERSON> chuyển features

1. Auth components và types
2. Jobs components, hooks, stores, types
3. Companies tương tự
4. User tương tự

## Phase 5: Cập nhật imports

1. Tạo index files cho exports
2. Cập nhật tsconfig.json paths
3. Update imports từng file

## Phase 6: Cleanup

1. Xóa folders cũ
2. Xóa files trùng lặp
3. Test build

## Phase 7: Testing

1. Test build
2. Test type checking
3. Test linting

Hiện tại: <PERSON><PERSON> thực hiện Phase 5
