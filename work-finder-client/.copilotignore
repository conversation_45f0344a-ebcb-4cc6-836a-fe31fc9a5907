# Files and directories to ignore for GitHub Copilot

# Dependencies
node_modules/
.pnpm-store/

# Build outputs
.next/
out/
dist/
build/

# Cache directories
.cache/
.turbo/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# TypeScript cache
*.tsbuildinfo

# Test coverage
coverage/

# Storybook
storybook-static/

# Documentation that shouldn't influence code generation
README.md
CHANGELOG.md
LICENSE

# Config files (let them be context but don't suggest changes)
next.config.*
tailwind.config.*
tsconfig.json
eslint.config.*
postcss.config.*