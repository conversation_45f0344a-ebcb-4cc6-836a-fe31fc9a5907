import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import {
  AUTH_ROUTES,
  DASHBOARD_ROUTES,
  EMPLOYER_ROUTES,
} from "./src/constants/routes";

// Define route patterns for authentication checks
const publicRoutes = [
  "/",
  "/jobs",
  "/companies",
  "/about",
  "/contact",
  "/privacy",
  "/terms",
  "/test-i18n", // Test page
];

const authRoutes = Object.values(AUTH_ROUTES);
const protectedRoutes = [
  ...Object.values(DASHBOARD_ROUTES),
  ...Object.values(EMPLOYER_ROUTES),
  "/dashboard", // All dashboard routes are protected
];

/**
 * Middleware for handling authentication and language preferences
 * This middleware works with the existing HttpOnly cookie authentication system
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/api/") ||
    pathname.includes(".") // Static files (images, fonts, etc.)
  ) {
    return NextResponse.next();
  }

  // Get authentication token from HttpOnly cookie
  // Check all possible cookie names the backend might use
  const accessToken =
    request.cookies.get("access_token")?.value ||
    request.cookies.get("accessToken")?.value ||
    request.cookies.get("session")?.value ||
    request.cookies.get("authToken")?.value ||
    request.cookies.get("token")?.value;

  const isAuthenticated = !!accessToken && accessToken.length > 0;

  console.log(`[Middleware] ${pathname} - Auth: ${isAuthenticated}`);

  // Check if current route is protected
  const isProtectedRoute = protectedRoutes.some(
    (route) => pathname === route || pathname.startsWith(route + "/")
  );

  // Check if current route is auth route
  const isAuthRoute = authRoutes.some(
    (route) => pathname === route || pathname.startsWith(route + "/")
  );

  // Simplified logging - detailed logs removed for cleaner output

  // Handle authentication redirects
  if (isProtectedRoute && !isAuthenticated) {
    // Redirect unauthenticated users to login
    const loginUrl = new URL("/login", request.url);
    loginUrl.searchParams.set("callbackUrl", pathname);
    return NextResponse.redirect(loginUrl);
  }

  if (isAuthRoute && isAuthenticated) {
    // Redirect authenticated users away from auth pages IMMEDIATELY
    const redirectTo = request.nextUrl.searchParams.get("redirect");

    // Default redirect based on user role (since we can't decode token in middleware, use default)
    let redirectUrl = redirectTo || "/"; // Default to home page

    // Validate redirectUrl to prevent open redirect attacks
    if (
      redirectUrl &&
      redirectUrl !== "/login" &&
      redirectUrl !== "/register" &&
      redirectUrl !== "/forgot-password" &&
      redirectUrl.startsWith("/") &&
      !redirectUrl.startsWith("//")
    ) {
      // Use the provided redirect
    } else {
      redirectUrl = "/"; // Safe fallback
    }

    // Create response with redirect and aggressive no-cache headers
    const response = NextResponse.redirect(new URL(redirectUrl, request.url), {
      status: 307, // Temporary redirect to preserve HTTP method
    });

    // Prevent any caching of auth redirects
    response.headers.set(
      "Cache-Control",
      "no-cache, no-store, must-revalidate, private"
    );
    response.headers.set("Pragma", "no-cache");
    response.headers.set("Expires", "0");
    response.headers.set("X-Accel-Expires", "0");
    response.headers.set("X-Middleware-Redirect", "auth-check");

    return response;
  }

  // Handle language preferences
  const response = NextResponse.next();

  // Get language preference from cookie or header
  const languageCookie = request.cookies.get("user-language")?.value;
  const acceptLanguage = request.headers.get("accept-language");

  // Set language preference in response headers for client-side access
  if (languageCookie) {
    response.headers.set("x-user-language", languageCookie);
  } else if (acceptLanguage) {
    // Parse Accept-Language header and set default if Vietnamese is preferred
    const preferredLanguage = acceptLanguage.includes("vi") ? "vi" : "en";
    response.headers.set("x-user-language", preferredLanguage);

    // Set cookie for future requests
    response.cookies.set("user-language", preferredLanguage, {
      path: "/",
      maxAge: 60 * 60 * 24 * 365, // 1 year
      sameSite: "lax",
      secure: process.env.NODE_ENV === "production",
    });
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\..*).*)",
  ],
};
