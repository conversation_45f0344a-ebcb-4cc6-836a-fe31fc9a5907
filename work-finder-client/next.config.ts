import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin();

const nextConfig: NextConfig = {
  eslint: {
    // Only lint during dev, not during build
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Type checking is handled separately in CI/CD
    ignoreBuildErrors: false,
  },
};

export default withNextIntl(nextConfig);
