# GitHub Copilot Instructions for Work Finder Client

Work Finder is a modern job search platform with Next.js 14 + TypeScript client and NestJS server. The architecture uses HttpOnly cookie authentication, Zustand state management, TanStack Query for data fetching, and shadcn/ui components.

## 🏗️ Architecture Overview

**Authentication Flow**: HttpOnly cookies with JWT tokens, automatic refresh on 401, middleware-based route protection  
**State Management**: Zustand with immer + persist middleware for client state, TanStack Query for server state  
**API Communication**: Native fetch() with `credentials: "include"`, standardized error handling via `handleResponse()`  
**Component Structure**: Feature-based organization under `src/components/`, strict TypeScript interfaces, shadcn/ui base  
**Internationalization**: next-intl with English/Vietnamese support, translation keys in `messages/` directory

## 🎯 Development Context Setup

Before coding, always reference these files for patterns:

- `src/types/` - Type definitions and interfaces
- `src/lib/utils.ts` - Utility functions and helpers
- `src/components/ui/` - Base UI component library
- `ARCHITECTURE.md` - Detailed architectural patterns
- `PATTERNS.md` - Quick reference code patterns

## 🚦 Essential Patterns

### Component Creation with Comments

Use descriptive comments to guide generation:

```typescript
// Create a job card component with bookmark/apply buttons, salary display, and hover states
interface JobCardProps {
  job: Job;
  onBookmark: (id: string) => void;
  onApply: (id: string) => void;
}

export function JobCard({ job, onBookmark, onApply }: JobCardProps) {
  // Component implementation follows automatically
}
```

### Effective Workspace Commands

Use `@workspace` for feature generation:

```
@workspace Create a job search filters component with dropdowns for location, salary range, job type using our shadcn/ui patterns and form validation.

@workspace Build a user dashboard showing application stats, recommended jobs, and quick actions following our responsive design patterns.

@workspace Generate a multi-step job posting form with validation, draft saving, and preview mode using our form patterns.
```

### API Functions

```typescript
// Always use handleResponse() and credentials: "include"
export async function createItem(data: CreateItemRequest): Promise<Item> {
  const response = await fetch(`${API_BASE_URL}/items`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    credentials: "include", // Required for HttpOnly cookies
    body: JSON.stringify(data),
  });

  const result = await handleResponse<{ item: Item }>(response);
  return result.item;
}
```

### Zustand Store

```typescript
// Use immer + persist + devtools pattern
export const useStore = create<StoreState>()(
  devtools(
    persist(
      immer((set) => ({
        items: [],
        setItems: (items) =>
          set((state) => {
            state.items = items;
          }),
      })),
      { name: "store-name", partialize: (state) => ({ items: state.items }) }
    ),
    { name: "store-name" }
  )
);
```

### Forms with Validation

```typescript
// Always use react-hook-form + zod + shadcn/ui form components
const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
});

export function FormComponent({
  onSubmit,
}: {
  onSubmit: (data: FormData) => Promise<void>;
}) {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: { name: "", email: "" },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
}
```

## 🔒 Authentication Patterns

**Critical**: All API calls MUST include `credentials: "include"` for HttpOnly cookie authentication. Use `fetchWithAuth()` from `@/lib/api/with-auth` for automatic token refresh on 401 responses.

```typescript
// For authenticated requests, use wrapper functions
import { fetchAuthPost, fetchAuthGet } from "@/lib/api/with-auth";

const response = await fetchAuthPost("/api/protected-endpoint", data);
```

**Route Protection**: Protected routes automatically redirect via middleware. Use `useAuth()` hook for role-based UI:

```typescript
const { isAuthenticated, isEmployer, isJobSeeker } = useAuth();
```

**Debugging Auth Issues**: Common auth problems and solutions:

- Infinite loading loops: Check useEffect dependencies in AppProvider
- Token persistence issues: Verify Zustand persist middleware configuration
- 401 errors: Ensure `credentials: "include"` in all fetch calls
- Cookie handling: Use browser DevTools to inspect HttpOnly cookies

## 🔧 Development Workflows

### Feature Development Process

1. Start with descriptive comment: `// Create a job application tracking system`
2. Use `@workspace` for structure: `@workspace Create components for job application flow`
3. Iterate with specific requests: `@workspace Add loading states and error handling`
4. Request tests: `@workspace Generate comprehensive tests for this feature`

### Debugging Workflow

1. Describe issue in comment: `// BUG: User dropdown shows infinite loading after login`
2. Analyze: `@workspace Analyze this component for infinite loop issues`
3. Fix: `@workspace Suggest fixes for the useEffect dependency issues`

### Code Review Process

Use `@workspace` to review code quality:

- `@workspace Review this component for performance issues and suggest optimizations`
- `@workspace Check this authentication code for security vulnerabilities`
- `@workspace Optimize this code for better accessibility and mobile responsiveness`

## 🌐 Internationalization

Always use translation keys, never hardcoded strings:

```typescript
import { useTranslations } from "next-intl";

export function Component() {
  const t = useTranslations(); // or useTranslations("namespace")
  return <h1>{t("title")}</h1>;
}
```

## 🚫 Critical Don'ts

- Never use `any` types - strict TypeScript is enforced
- Never hardcode API URLs - use `getApiBaseUrl()` from `@/lib/api/utils`
- Never forget `credentials: "include"` in fetch calls
- Never use direct DOM manipulation - use React patterns
- Never use inline styles - use Tailwind classes with `cn()` utility
- Never mutate state directly - use immer patterns in Zustand stores
- Never accept Copilot suggestions blindly - always review for security and performance
- Never hardcode text strings - use next-intl translation keys

## 🎯 Pro Tips for Effective Usage

**Be Specific with Context**: Instead of "Create a form", use "Create a job application form with file upload, using our form validation patterns, error handling, and loading states"

**Reference Existing Patterns**: Use "Create a component similar to JobCard but for company profiles" to leverage existing code patterns

**Iterative Refinement**: Ask for improvements with "This component is good but can you optimize it for better performance and add error boundaries?"

**Cross-Reference Files**: Update components with "Update this component to match the patterns used in UserDropdown.tsx"

## 🔧 Key Directories

**Components**: Feature-based in `src/components/{auth,features,ui,shared}/`  
**API Functions**: Organized by domain in `src/lib/api/{auth,jobs,companies}.ts`  
**State Stores**: Domain-specific in `src/stores/{user,job}-store.ts`  
**Types**: Centralized in `src/types/` with domain-specific files  
**Hooks**: Custom hooks in `src/hooks/` following `use-{domain}.ts` pattern

## 📋 Quick Command Reference

| Task             | Command Template                                                                      |
| ---------------- | ------------------------------------------------------------------------------------- |
| Create Component | `@workspace Create a [component type] that [requirements] using our [pattern/system]` |
| Debug Issues     | `@workspace Debug this [issue type] in [component/file] and suggest fixes`            |
| Add Tests        | `@workspace Generate tests for this [component] covering [scenarios]`                 |
| Optimize Code    | `@workspace Optimize this code for [performance/accessibility/security]`              |
| Refactor         | `@workspace Refactor this to use [new pattern] while maintaining [requirements]`      |
