// Simplified user type for auth purposes - matches backend response
export interface AuthUser {
  user_id: number;
  username: string;
  role: "job_seeker" | "employer" | "admin";
  email?: string;
  full_name?: string;
}

// Transform backend user to frontend user structure
export function transformAuthUser(backendUser: AuthUser): Partial<User> {
  return {
    id: backendUser.user_id.toString(),
    username: backendUser.username,
    email: backendUser.email || backendUser.username + '@example.com', // fallback
    name: backendUser.full_name || backendUser.username,
    role: backendUser.role,
    // Add default values for other required fields
    profile: {
      firstName: backendUser.full_name?.split(' ')[0] || backendUser.username,
      lastName: backendUser.full_name?.split(' ')[1] || '',
      skills: [],
      experience: [],
      education: [],
      preferences: {
        desiredRoles: [],
        preferredLocations: [],
        workType: [],
        employmentType: [],
        salaryExpectation: {
          min: 0,
          max: 0,
          currency: 'USD',
          period: 'yearly'
        },
        willingToRelocate: false
      }
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  } as User;
}

// Import User type
import type { User } from './user';