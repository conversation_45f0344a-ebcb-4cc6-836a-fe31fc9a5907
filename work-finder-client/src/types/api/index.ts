// API-specific types
import type { Paginated, ApiResponse } from '../common';

// Request types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  termsAccepted: boolean;
}

export interface JobSearchRequest {
  search?: string;
  location?: string;
  employmentType?: string[];
  experienceLevel?: string[];
  salary?: {
    min?: number;
    max?: number;
  };
  skills?: string[];
  companySize?: string[];
  industry?: string[];
  postedWithin?: string;
  page?: number;
  limit?: number;
  sortBy?: 'relevance' | 'date' | 'salary';
  sortOrder?: 'asc' | 'desc';
}

// Response types
export interface AuthResponse {
  user: any; // Will be replaced with User type from domain
  token: string;
  refreshToken?: string;
  expiresAt: string;
}

export interface JobSearchResponse extends Paginated<any> {
  filters: {
    employmentTypes: string[];
    experienceLevels: string[];
    industries: string[];
    locations: string[];
    skills: string[];
  };
}