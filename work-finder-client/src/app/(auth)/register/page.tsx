import { redirect } from "next/navigation";
import { getServerAuthState } from "@/lib/auth/server-auth";
import RegisterForm from "./register-form";

export default async function RegisterPage() {
  // Server-side auth check - runs before any client code
  const { isAuthenticated } = await getServerAuthState();

  console.log("[RegisterPage] Server-side check result:", isAuthenticated);

  if (isAuthenticated) {
    console.log("[RegisterPage] Redirecting authenticated user to home");
    // Immediate server redirect - no client rendering
    redirect("/");
  }

  console.log(
    "[RegisterPage] Rendering register form for unauthenticated user"
  );
  // User not authenticated - render register form
  return <RegisterForm />;
}
