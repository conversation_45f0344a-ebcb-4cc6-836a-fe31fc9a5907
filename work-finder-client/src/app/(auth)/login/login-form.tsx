"use client";

import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { Button } from "@/components";
import { Form, FormField } from "@/components";
import { TextFormField, PasswordFormField } from "@/components";
import { Checkbox } from "@/components";
import { Label } from "@/components";
import { useAuthStore } from "@/stores/user-store";
import { AUTH_ROUTES } from "@/constants/routes";
import { useTranslation } from "@/hooks/useTranslation";
import { loginSchema, type LoginFormData } from "@/lib/validations/auth";

export default function LoginForm() {
  const router = useRouter();
  const { login: loginUser, isLoading } = useAuthStore();
  const { t } = useTranslation();

  const form = useForm<LoginFormData>({
    resolver: zod<PERSON><PERSON>olver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
      rememberMe: false,
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      await loginUser({
        username: data.username,
        password: data.password,
        rememberMe: data.rememberMe,
      });

      // Redirect to home or intended page after successful login
      const redirectUrl = new URLSearchParams(window.location.search).get('callbackUrl') || '/';
      router.push(redirectUrl);
    } catch (error) {
      console.error("Login failed:", error);
    }
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-xl font-bold text-gray-900 mb-1">
          {t("auth.loginTitle")}
        </h1>
        <p className="text-sm text-gray-600 mb-2">
          {t("metadata.loginDescription")}
        </p>
      </div>

      {/* Login Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
          <div className="space-y-4">
            {/* Username */}
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <TextFormField
                  {...field}
                  label={t("auth.username")}
                  placeholder={t("auth.usernamePlaceholder")}
                  required
                />
              )}
            />

            {/* Password */}
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <PasswordFormField
                  {...field}
                  label={t("auth.password")}
                  placeholder={t("auth.passwordPlaceholder")}
                  required
                />
              )}
            />

            {/* Remember Me */}
            <FormField
              control={form.control}
              name="rememberMe"
              render={({ field }) => (
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                  <Label className="text-xs text-gray-700">
                    {t("auth.rememberMe")}
                  </Label>
                </div>
              )}
            />
          </div>

          <Button
            type="submit"
            disabled={isLoading}
            className="w-full h-10 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium text-sm rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            {isLoading ? t("auth.loggingIn") : t("auth.loginButton")}
          </Button>
        </form>
      </Form>

      {/* Forgot Password Link */}
      <div className="text-center">
        <Link
          href={AUTH_ROUTES.FORGOT_PASSWORD}
          className="text-xs text-blue-600 hover:text-blue-700 transition-colors duration-200"
        >
          {t("auth.forgotPassword")}
        </Link>
      </div>

      {/* Sign up link */}
      <div className="text-center">
        <p className="text-xs text-gray-600">
          {t("auth.dontHaveAccount")}{" "}
          <Link
            href={AUTH_ROUTES.REGISTER}
            className="font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200"
          >
            {t("auth.registerButton")}
          </Link>
        </p>
      </div>
    </div>
  );
}