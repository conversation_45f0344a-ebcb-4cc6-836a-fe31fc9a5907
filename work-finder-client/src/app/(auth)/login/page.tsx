import { redirect } from 'next/navigation';
import { getServerAuthState } from '@/lib/auth/server-auth';
import LoginForm from './login-form';

export default async function LoginPage() {
  // Server-side auth check - runs before any client code
  const { isAuthenticated } = await getServerAuthState();
  
  console.log('[LoginPage] Server-side check result:', isAuthenticated);
  
  if (isAuthenticated) {
    console.log('[LoginPage] Redirecting authenticated user to home');
    // Immediate server redirect - no client rendering
    redirect('/');
  }
  
  console.log('[LoginPage] Rendering login form for unauthenticated user');
  // User not authenticated - render login form
  return <LoginForm />;
}