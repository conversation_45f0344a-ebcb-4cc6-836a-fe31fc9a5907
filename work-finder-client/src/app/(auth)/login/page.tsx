import { redirect } from 'next/navigation';
import { getServerAuthState } from '@/lib/auth/server-auth';
import LoginForm from './login-form';

export default async function LoginPage() {
  // Server-side auth check - runs before any client code
  const { isAuthenticated } = await getServerAuthState();
  
  if (isAuthenticated) {
    // Immediate server redirect - no client rendering
    redirect('/');
  }
  
  // User not authenticated - render login form
  return <LoginForm />;
}