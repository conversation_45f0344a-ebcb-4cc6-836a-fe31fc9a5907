import type { <PERSON>ada<PERSON> } from "next";
import { Jo<PERSON> } from "next/font/google";
import { getLocale, getMessages } from "next-intl/server";
import { AppProviders } from "@/components/providers/app-providers";
import { ConditionalLayout } from "@/components/layout";
import "./globals.css";

const jost = Jost({
  variable: "--font-jost",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Work Finder - Find Your Dream Job",
  description:
    "Discover thousands of job opportunities and connect with top employers. Your next career move starts here.",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body
        className={`${jost.variable} antialiased font-jost`}
        suppressHydrationWarning
      >
        <AppProviders locale={locale} messages={messages}>
          <ConditionalLayout>{children}</ConditionalLayout>
        </AppProviders>
      </body>
    </html>
  );
}
