"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuthStore } from "@/stores/user-store";

/**
 * Hook to handle authentication-based redirects
 * Redirects authenticated users away from auth pages
 * Note: Middleware handles most redirects, this is a fallback for client-side navigation
 */
export function useAuthRedirect() {
  // Disabled for now - handled by server-side protection
  // This prevents conflicts with auth pages
}

/**
 * Hook to get user authentication status and role
 */
export function useAuth() {
  const { user, isAuthenticated, isLoading, isInitializing } = useAuthStore();

  return {
    user,
    isAuthenticated,
    isLoading,
    isInitializing,
    isJobSeeker: user?.role === "job_seeker",
    isEmployer: user?.role === "employer",
    isAdmin: user?.role === "admin",
  };
}
