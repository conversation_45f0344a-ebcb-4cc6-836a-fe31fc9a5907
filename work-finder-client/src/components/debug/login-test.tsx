"use client";

import { useState } from "react";
import { useAuthStore } from "@/stores/user-store";

export function LoginTest() {
  const [username, setUsername] = useState("testuser");
  const [password, setPassword] = useState("password123");
  const { login, logout, getCurrentUser, user, isAuthenticated, isLoading } = useAuthStore();

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const handleLogin = async () => {
    try {
      console.log('🧪 [LoginTest] Attempting login...');
      await login({ username, password, rememberMe: false });
      console.log('🧪 [LoginTest] Login completed');
    } catch (error) {
      console.error('🧪 [LoginTest] Login failed:', error);
    }
  };

  const handleLogout = async () => {
    try {
      console.log('🧪 [LoginTest] Attempting logout...');
      await logout();
      console.log('🧪 [LoginTest] Logout completed');
    } catch (error) {
      console.error('🧪 [LoginTest] Logout failed:', error);
    }
  };

  const handleGetCurrentUser = async () => {
    try {
      console.log('🧪 [LoginTest] Getting current user...');
      await getCurrentUser();
      console.log('🧪 [LoginTest] Get current user completed');
    } catch (error) {
      console.error('🧪 [LoginTest] Get current user failed:', error);
    }
  };

  const checkCookies = async () => {
    console.log('🧪 [LoginTest] All visible cookies:', document.cookie);
    console.log('🧪 [LoginTest] Note: HttpOnly cookies are not visible to JavaScript');
    
    // Test if cookies work by making an API call
    try {
      const response = await fetch('http://localhost:3001/api/v1/auth/me', {
        credentials: 'include'
      });
      console.log('🧪 [LoginTest] Auth test response:', response.status);
      if (response.ok) {
        console.log('🧪 [LoginTest] ✅ HttpOnly auth cookies are working');
      } else {
        console.log('🧪 [LoginTest] ❌ No valid auth cookies');
      }
    } catch (error) {
      console.log('🧪 [LoginTest] ❌ Auth test failed:', error);
    }
  };

  return (
    <div className="fixed bottom-4 left-4 bg-white border border-gray-300 p-4 rounded-lg text-sm max-w-xs z-[9999] shadow-lg">
      <div className="font-bold mb-2">🧪 Login Test</div>
      
      <div className="space-y-2">
        <input
          type="text"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          placeholder="Username"
          className="w-full p-1 border rounded text-xs"
        />
        
        <input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="Password"
          className="w-full p-1 border rounded text-xs"
        />
        
        <div className="grid grid-cols-2 gap-1">
          <button
            onClick={handleLogin}
            disabled={isLoading}
            className="bg-blue-500 text-white p-1 rounded text-xs hover:bg-blue-600 disabled:opacity-50"
          >
            {isLoading ? 'Loading...' : 'Login'}
          </button>
          
          <button
            onClick={handleLogout}
            className="bg-red-500 text-white p-1 rounded text-xs hover:bg-red-600"
          >
            Logout
          </button>
        </div>
        
        <div className="grid grid-cols-2 gap-1">
          <button
            onClick={handleGetCurrentUser}
            className="bg-green-500 text-white p-1 rounded text-xs hover:bg-green-600"
          >
            Get User
          </button>
          
          <button
            onClick={checkCookies}
            className="bg-purple-500 text-white p-1 rounded text-xs hover:bg-purple-600"
          >
            Check Cookies
          </button>
        </div>
        
        <div className="text-xs">
          Status: <span className={isAuthenticated ? "text-green-600" : "text-red-600"}>
            {isAuthenticated ? '✅ Authenticated' : '❌ Not authenticated'}
          </span>
        </div>
        
        {user && (
          <div className="text-xs text-blue-600">
            User: {user.username} ({user.role})
          </div>
        )}
      </div>
    </div>
  );
}