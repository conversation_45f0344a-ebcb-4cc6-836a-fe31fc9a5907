"use client";

import { useAuthStore } from "@/stores/user-store";
import { useEffect, useState } from "react";

export function AuthDebug() {
  const { user, isAuthenticated, isLoading, isInitializing } = useAuthStore();
  const [cookies, setCookies] = useState<string>("");

  useEffect(() => {
    // Get all cookies
    setCookies(document.cookie);
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm z-[9999]">
      <div className="font-bold mb-2">🔧 Auth Debug</div>
      
      <div className="space-y-1">
        <div>isInitializing: <span className={isInitializing ? "text-yellow-300" : "text-green-300"}>{String(isInitializing)}</span></div>
        <div>isLoading: <span className={isLoading ? "text-yellow-300" : "text-green-300"}>{String(isLoading)}</span></div>
        <div>isAuthenticated: <span className={isAuthenticated ? "text-green-300" : "text-red-300"}>{String(isAuthenticated)}</span></div>
        <div>user: <span className="text-blue-300">{user ? `${user.username} (${user.role})` : "null"}</span></div>
        
        <div className="mt-2 pt-2 border-t border-gray-600">
          <div>Visible cookies:</div>
          <div className="text-yellow-300 break-all text-xs">
            {cookies ? cookies : 'None visible'}
          </div>
          <div className="text-xs text-gray-400 mt-1">
            Note: HttpOnly auth cookies not visible to JS
          </div>
        </div>
      </div>
    </div>
  );
}