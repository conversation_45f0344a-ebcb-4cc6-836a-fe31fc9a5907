"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuthRedirect";

interface AuthGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
}

/**
 * Guard that redirects authenticated users away from auth pages
 * Use this on login, register, forgot-password pages
 */
export function AuthGuard({ children, redirectTo }: AuthGuardProps) {
  const router = useRouter();
  const { user, isAuthenticated, isInitializing } = useAuth();

  // Always call all hooks in the same order
  useEffect(() => {
    // Only redirect after initialization is complete
    if (!isInitializing && isAuthenticated && user) {
      const destination = redirectTo || 
        (user.role === "job_seeker" ? "/" : 
         user.role === "employer" ? "/employer/dashboard" : 
         user.role === "admin" ? "/admin" : "/dashboard");
      
      console.log(`[AuthGuard] Redirecting authenticated user to: ${destination}`);
      router.replace(destination);
    }
  }, [isAuthenticated, user, isInitializing, router, redirectTo]);

  // Show loading during initialization
  if (isInitializing) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing...</p>
        </div>
      </div>
    );
  }

  // Show loading if authenticated (during redirect)
  if (isAuthenticated && user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting...</p>
        </div>
      </div>
    );
  }

  // User is not authenticated, show the auth page
  return <>{children}</>;
}