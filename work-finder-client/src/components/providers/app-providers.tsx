"use client";

import { type ReactNode, useEffect } from "react";
import { NextIntlClientProvider } from "next-intl";
import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ThemeProvider as NextThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/atoms/sonner";
import { LanguageLoadingHandler } from "@/components/ui/atoms/language-loading";
import { useAuthStore } from "@/stores/user-store";
import { queryClient } from "@/lib/query-client";

interface AppProvidersProps {
  children: ReactNode;
  locale: string;
  messages?: Record<string, unknown>;
}

function AuthInitializer({ children }: { children: ReactNode }) {
  const { getCurrentUser, isInitializing } = useAuthStore();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        await getCurrentUser();
      } catch (error) {
        // Silent fail - user not authenticated
      }
    };

    // Only initialize once and ensure we don't re-run on re-renders
    if (isInitializing) {
      initializeAuth();
    }
  }, [getCurrentUser, isInitializing]); // Add dependencies but prevent loops

  // Show loading spinner until auth is initialized
  if (isInitializing) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-sm">Checking authentication...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

export function AppProviders({ children, locale, messages }: AppProvidersProps) {
  return (
    <NextIntlClientProvider locale={locale} messages={messages}>
      <NextThemeProvider
        attribute="class"
        defaultTheme="light" 
        enableSystem
        disableTransitionOnChange
      >
        <QueryClientProvider client={queryClient}>
          <AuthInitializer>
            <LanguageLoadingHandler />
            {children}
            <Toaster
              position="top-right"
              richColors
              closeButton
              duration={4000}
            />
            <ReactQueryDevtools initialIsOpen={false} />
          </AuthInitializer>
        </QueryClientProvider>
      </NextThemeProvider>
    </NextIntlClientProvider>
  );
}
