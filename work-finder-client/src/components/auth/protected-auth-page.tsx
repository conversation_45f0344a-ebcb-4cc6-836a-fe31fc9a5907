"use client";

import { useEffect, useState } from "react";
import { useAuthStore } from "@/stores/user-store";
import { useRouter } from "next/navigation";

interface ProtectedAuthPageProps {
  children: React.ReactNode;
}

/**
 * Hydration-safe auth protection component
 */
export function ProtectedAuthPage({ children }: ProtectedAuthPageProps) {
  const { isAuthenticated, user, isInitializing } = useAuthStore();
  const [isHydrated, setIsHydrated] = useState(false);
  const [shouldRender, setShouldRender] = useState(true); // Default to true to match SSR
  const router = useRouter();

  // Handle hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Handle auth redirect after hydration
  useEffect(() => {
    if (!isHydrated || isInitializing) return;

    if (isAuthenticated && user) {
      // Authenticated user - redirect immediately
      const destination = user.role === "job_seeker" ? "/" : 
                         user.role === "employer" ? "/employer/dashboard" : 
                         "/";
      router.replace(destination);
      setShouldRender(false);
      return;
    }

    // Not authenticated - safe to render
    setShouldRender(true);
  }, [isAuthenticated, user, isInitializing, router, isHydrated]);

  // Show loading during hydration or auth initialization
  if (!isHydrated || isInitializing) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show loading if authenticated (during redirect)
  if (isAuthenticated && user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Safe to render auth page
  return shouldRender ? <>{children}</> : null;
}