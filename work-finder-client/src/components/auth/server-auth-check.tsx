import { redirect } from 'next/navigation';
import { checkAuthPageAccess } from '@/lib/auth/server-auth';

interface ServerAuthCheckProps {
  children: React.ReactNode;
}

/**
 * Server component that checks authentication before rendering
 * This prevents any client-side flash by checking auth on server
 */
export async function ServerAuthCheck({ children }: ServerAuthCheckProps) {
  try {
    const { shouldRedirect, redirectTo } = await checkAuthPageAccess();
    
    if (shouldRedirect && redirectTo) {
      // Immediate server redirect - no client code will run
      redirect(redirectTo);
    }
  } catch (error) {
    // If auth check fails, allow access to auth pages
    console.log('[ServerAuthCheck] Auth check failed, allowing access');
  }
  
  // User is not authenticated, render children
  return <>{children}</>;
}