import { cookies } from 'next/headers';

/**
 * Server-side authentication checker
 * This runs on the server before any client-side code
 */
export async function getServerAuthState() {
  const cookieStore = await cookies();
  
  // Check for authentication token in cookies
  const accessToken = cookieStore.get('access_token')?.value ||
                     cookieStore.get('accessToken')?.value ||
                     cookieStore.get('session')?.value ||
                     cookieStore.get('authToken')?.value ||
                     cookieStore.get('token')?.value;

  const isAuthenticated = !!accessToken && accessToken.length > 0;
  
  return {
    isAuthenticated,
    hasToken: !!accessToken
  };
}

/**
 * Server-side auth check for auth pages
 * Returns redirect response if user is authenticated
 */
export async function checkAuthPageAccess() {
  const { isAuthenticated } = await getServerAuthState();
  
  if (isAuthenticated) {
    return { shouldRedirect: true, redirectTo: '/' };
  }
  
  return { shouldRedirect: false };
}