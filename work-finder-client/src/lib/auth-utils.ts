/**
 * Authentication utilities for checking auth state
 * Since HttpOnly cookies can't be read by JS across different domains,
 * we'll use a quick API check instead
 */

import { getApiBaseUrl } from './api/utils';

/**
 * Check if user is authenticated by making a quick API call
 * This replaces cookie checking since HttpOnly cookies aren't accessible via JS
 * when frontend and backend are on different ports
 */
export async function checkAuthStatus(): Promise<boolean> {
  if (typeof window === 'undefined') return false;
  
  try {
    console.log('[AuthUtils] Checking auth status via API...');
    
    const response = await fetch(`${getApiBaseUrl()}/auth/me`, {
      method: 'GET',
      credentials: 'include',
      // Add short timeout to avoid hanging
      signal: AbortSignal.timeout(3000)
    });
    
    const isAuthenticated = response.ok;
    console.log('[AuthUtils] Auth check result:', isAuthenticated);
    
    return isAuthenticated;
  } catch (error) {
    console.log('[AuthUtils] Auth check failed (likely not authenticated):', error);
    return false;
  }
}

/**
 * Get cookie value by name (client-side only)
 * Note: This won't work for HttpOnly cookies from different domains
 */
export function getCookie(name: string): string | null {
  if (typeof window === 'undefined') return null;
  
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  
  if (parts.length === 2) {
    const cookieValue = parts.pop()?.split(';').shift();
    return cookieValue || null;
  }
  
  return null;
}

/**
 * Legacy function - replaced by checkAuthStatus()
 * Kept for backward compatibility but won't work with cross-domain HttpOnly cookies
 */
export function hasValidAccessToken(): boolean {
  const token = getCookie('access_token');
  
  // Debug logging
  console.log('[AuthUtils] Current URL:', window.location.href);
  console.log('[AuthUtils] All cookies:', document.cookie);
  console.log('[AuthUtils] Access token found:', token);
  
  return !!(token && token.trim() && token !== 'undefined' && token !== 'null');
}