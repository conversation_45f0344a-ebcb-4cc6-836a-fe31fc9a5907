{"permissions": {"allow": ["Bash(rm:*)", "Bash(npm run build:*)", "Bash(NEXT_LINT=false npm run build)", "<PERSON><PERSON>(mkdir:*)", "Bash(for feature in auth jobs companies dashboard user)", "Bash(do mkdir -p $feature/{components,hooks,stores,services,types,utils})", "Bash(done)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(find:*)", "<PERSON><PERSON>(touch:*)", "Bash(cp:*)", "<PERSON><PERSON>(npx husky:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git stash:*)", "Bash(npx tsc:*)", "Bash(node:*)", "Bash(npm run type-check:*)", "Bash(ls:*)", "Bash(git checkout:*)", "Bash(npm run lint)", "Bash(npm install:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(cat:*)", "Bash(# Categories\necho ''export function CategoryCard({ category, className }: any) { return <div className={className}>Category Card</div>; }'' > /Users/<USER>/Documents/WorkFinder/work-finder-client/src/features/categories/components/category-card.tsx\necho ''export function CategoryGrid({ categories, className }: any) { return <div className={className}>Category Grid</div>; }'' > /Users/<USER>/Documents/WorkFinder/work-finder-client/src/features/categories/components/category-grid.tsx\n\n# Search  \necho ''export function ModernSearchFilters({ onFiltersChange, className }: any) { return <div className={className}>Modern Search Filters</div>; }'' > /Users/<USER>/Documents/WorkFinder/work-finder-client/src/features/search/components/modern-search-filters.tsx\necho ''export function SearchFilters({ onFiltersChange, className }: any) { return <div className={className}>Search Filters</div>; }'' > /Users/<USER>/Documents/WorkFinder/work-finder-client/src/features/search/components/search-filters.tsx\necho ''export function SearchForm({ onSearch, className }: any) { return <div className={className}>Search Form</div>; }'' > /Users/<USER>/Documents/WorkFinder/work-finder-client/src/features/search/components/search-form.tsx\n\n# User\necho ''export function ProfileForm({ user, onSave, className }: any) { return <div className={className}>Profile Form</div>; }'' > /Users/<USER>/Documents/WorkFinder/work-finder-client/src/features/user/components/profile-form.tsx)", "WebFetch(domain:nextjs.org)", "WebFetch(domain:stackoverflow.com)", "WebFetch(domain:joshuaclaudioenrico.medium.com)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "Bash(git log:*)", "<PERSON><PERSON>(pkill:*)"], "deny": []}}